<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>projeto lafa</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <div class="menu-icon" onclick="toggleMenu()">
            <span></span>
            <span></span>
            <span></span>
        </div>
        <div class="header-logo">
            <img src="img/Logo Principal s Fundo (1).png" alt="Logo">
        </div>
        <div class="search-container">
            <input type="text" class="search-input" placeholder="Pesquisar..." id="searchInput">
            <div class="search-icon" onclick="toggleSearch()">⌕</div>
        </div>
    </header>

    <div class="blur-bar"></div>

  
    <nav class="side-menu" id="sideMenu">
        <div class="menu-overlay" onclick="closeMenu()"></div>
        <div class="menu-content">
            <div class="menu-header">
                <h2>Menu</h2>
                <button class="close-btn" onclick="closeMenu()">×</button>
            </div>
            <ul class="menu-items">
                <li><a href="#home">Home</a></li>
                <li><a href="#quem-somos">Quem Somos</a></li>
                <li><a href="#areas-atuacao">Áreas de Atuação</a></li>
                <li><a href="#contatos">Contatos</a></li>
            </ul>
        </div>
    </nav>

   
    <section class="info-slider">
        <div class="slider-container">
            <div class="slider-wrapper">
                <div class="slide active">
                    <div class="slide-content">
                        <img src="img/patrick-fore-H5Lf0nGyetk-unsplash.jpg" alt="Direito Criminal">
                        <div class="slide-text">
                            <h2>Atendimento Jurídico na Área Penal</h2>
                        </div>
                    </div>
                </div>
                <div class="slide">
                    <div class="slide-content">
                        <img src="img/giammarco-boscaro-eWpBNXRHfTI-unsplash.jpg">
                    </div>
                </div>
                <div class="slide">
                    <div class="slide-content">
                        <img src="img/nasser-eledroos-20fmGtxKs_I-unsplash.jpg">
                    </div>
                </div>
                <div class="slide">
                    <div class="slide-content">
                        <img src="img/pexels-ekaterina-bolovtsova-6077181.jpg">
                    </div>
                </div>
            </div>

            
            <button class="slider-btn prev-btn" onclick="changeSlide(-1)">‹</button>
            <button class="slider-btn next-btn" onclick="changeSlide(1)">›</button>


            <div class="slider-indicators">
                <span class="indicator active" onclick="currentSlide(1)"></span>
                <span class="indicator" onclick="currentSlide(2)"></span>
                <span class="indicator" onclick="currentSlide(3)"></span>
                <span class="indicator" onclick="currentSlide(4)"></span>
            </div>
        </div>
    </section>

    <!-- Seção Quem Somos -->
    <section class="quem-somos" id="quem-somos">
        <div class="container">
            <div class="quem-somos-content">
                <div class="text-content">
                    <h2>Quem Somos</h2>
                    <div class="text-blocks">
                        <p>O escritório <strong>Demetre & Lins Advocacia Penal</strong> é uma iniciativa comprometida com a justiça, o respeito à dignidade humana e a excelência no atendimento jurídico. Atuando exclusivamente na área penal, buscamos oferecer um serviço técnico, humanizado e acessível, voltado para a defesa dos direitos e garantias individuais de nossos clientes.</p>

                        <p>Fundado pelos advogados <strong>Maria Baltas Demetre</strong> e <strong>Vitor Lins</strong>, o escritório é composto por uma equipe multidisciplinar que une experiência, dedicação e colaboração. Nossa estrutura organizacional inclui ainda a advogada associada <strong>Anna Rondon</strong>, responsável pelo suporte técnico-jurídico, e um time administrativo composto por profissionais das áreas de gestão, finanças e marketing jurídico.</p>

                        <p>Acreditamos que a advocacia penal deve ir além da técnica: deve ser feita com empatia, escuta ativa e profundo respeito às particularidades de cada caso. Por isso, priorizamos um relacionamento próximo e eficiente com nossos clientes, adotando uma abordagem moderna e flexível, com atendimentos presenciais e virtuais, sempre com o mesmo nível de profissionalismo e sigilo.</p>
                    </div>
                </div>
                <div class="image-content">
                    <div class="image-placeholder">
                        <span>Adicione sua foto aqui</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Seção Nossa Equipe -->
    <section class="nossa-equipe" id="nossa-equipe">
        <div class="container">
            <div class="section-header">
                <h2>Nossa Equipe</h2>
                <p class="section-intro">A força do escritório Demetre & Lins Advocacia Penal está na união de profissionais comprometidos com a justiça, a ética e o trabalho em equipe. Cada integrante desempenha um papel essencial na construção de soluções jurídicas personalizadas e eficientes, atuando com dedicação e responsabilidade.</p>
            </div>

            <!-- Sócios Fundadores -->
            <div class="team-category">
                <h3 class="category-title">Sócios Fundadores</h3>
                <div class="team-grid founders">
                    <div class="team-member">
                        <div class="member-photo">
                            <div class="photo-placeholder">
                                <span>Foto</span>
                            </div>
                        </div>
                        <div class="member-info">
                            <h4>Maria Baltas Demetre</h4>
                            <span class="position">Advogada – Sócia Fundadora</span>
                            <p>Responsável pela condução estratégica dos casos e pela representação do escritório em causas de alta complexidade. Atua diretamente com os clientes, promovendo um atendimento humanizado e técnico.</p>
                        </div>
                    </div>
                    <div class="team-member">
                        <div class="member-photo">
                            <div class="photo-placeholder">
                                <span>Foto</span>
                            </div>
                        </div>
                        <div class="member-info">
                            <h4>Vitor Lins</h4>
                            <span class="position">Advogado – Sócio Fundador</span>
                            <p>Atua na coordenação das linhas de defesa, planejamento jurídico e interlocução com os parceiros externos. Preza pela excelência técnica e pelo compromisso ético na advocacia penal.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advogada Associada -->
            <div class="team-category">
                <h3 class="category-title">Advogada Associada</h3>
                <div class="team-grid associate">
                    <div class="team-member">
                        <div class="member-photo">
                            <div class="photo-placeholder">
                                <span>Foto</span>
                            </div>
                        </div>
                        <div class="member-info">
                            <h4>Anna Rondon</h4>
                            <span class="position">Advogada Associada – Suporte Técnico-Jurídico</span>
                            <p>Responsável pelo apoio direto aos sócios na elaboração de peças, estudos de caso e acompanhamento processual. Contribui com sua expertise para garantir agilidade e qualidade na execução das demandas jurídicas.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Equipe Administrativa -->
            <div class="team-category">
                <h3 class="category-title">Equipe Administrativa</h3>
                <div class="team-grid administrative">
                    <div class="team-member">
                        <div class="member-photo">
                            <div class="photo-placeholder">
                                <span>Foto</span>
                            </div>
                        </div>
                        <div class="member-info">
                            <h4>Marília Okawa</h4>
                            <span class="position">Assessora Administrativa</span>
                            <p>Atua na organização interna do escritório, garantindo a fluidez dos processos, a gestão de documentos e o suporte geral à equipe jurídica e operacional.</p>
                        </div>
                    </div>
                    <div class="team-member">
                        <div class="member-photo">
                            <div class="photo-placeholder">
                                <span>Foto</span>
                            </div>
                        </div>
                        <div class="member-info">
                            <h4>Yasmin Pereira</h4>
                            <span class="position">Assessora Financeira</span>
                            <p>Responsável pelo planejamento financeiro básico, controle de custos e organização orçamentária, com foco na sustentabilidade e crescimento do escritório.</p>
                        </div>
                    </div>
                    <div class="team-member">
                        <div class="member-photo">
                            <div class="photo-placeholder">
                                <span>Foto</span>
                            </div>
                        </div>
                        <div class="member-info">
                            <h4>Caroline Goes</h4>
                            <span class="position">Assessora de Marketing Jurídico</span>
                            <p>Desenvolve o portfólio de serviços, estratégias de posicionamento digital e cuida da imagem institucional, sempre respeitando os limites éticos da publicidade advocatícia.</p>
                        </div>
                    </div>
                    <div class="team-member">
                        <div class="member-photo">
                            <div class="photo-placeholder">
                                <span>Foto</span>
                            </div>
                        </div>
                        <div class="member-info">
                            <h4>Larissa Glória</h4>
                            <span class="position">Assistente Administrativa</span>
                            <p>Atua no apoio logístico e operacional do dia a dia, auxiliando na organização de agendas, atendimento inicial e suporte à equipe administrativa.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Seção Áreas de Atuação -->
    <section class="areas-atuacao" id="areas-atuacao">
        <div class="container">
            <div class="section-header">
                <h2>Áreas de Atuação</h2>
                <div class="intro-text">
                    <p>O escritório Demetre & Lins Advocacia Penal é especializado exclusivamente no Direito Penal, atuando na defesa de clientes em todas as fases do processo criminal, com responsabilidade, técnica e comprometimento com os direitos fundamentais.</p>
                    <p>Nosso trabalho abrange desde a consultoria preventiva até a atuação contenciosa, sempre com uma abordagem estratégica e humanizada, buscando soluções eficazes para cada caso concreto.</p>
                </div>
            </div>

            <div class="areas-category">
                <h3 class="category-title">Principais Frentes de Atuação</h3>
                <div class="areas-grid">
                    <div class="area-card">
                        <div class="area-icon">⚖️</div>
                        <h4>Crimes Contra a Pessoa</h4>
                        <p>Defesa em casos envolvendo homicídio, lesão corporal, ameaça, entre outros, com atuação firme e sensível às particularidades de cada situação.</p>
                    </div>

                    <div class="area-card">
                        <div class="area-icon">🏛️</div>
                        <h4>Crimes Contra o Patrimônio</h4>
                        <p>Atuação em casos de furto, roubo, estelionato, receptação, entre outros, buscando garantir o devido processo legal e a justa apuração dos fatos.</p>
                    </div>

                    <div class="area-card">
                        <div class="area-icon">🏢</div>
                        <h4>Crimes Contra a Administração Pública</h4>
                        <p>Defesa de acusados em processos envolvendo corrupção, peculato, improbidade e outros crimes funcionais, com foco técnico e estratégico.</p>
                    </div>

                    <div class="area-card">
                        <div class="area-icon">🌱</div>
                        <h4>Crimes Ambientais</h4>
                        <p>Parcerias com profissionais especializados para atuação em casos de dano ambiental, poluição, crimes contra a fauna e flora, entre outros.</p>
                    </div>

                    <div class="area-card">
                        <div class="area-icon">💻</div>
                        <h4>Crimes Cibernéticos e Digitais</h4>
                        <p>Enfrentamento de acusações ligadas a fraudes eletrônicas, invasão de dispositivos, falsidade documental digital, com atuação atualizada e eficaz.</p>
                    </div>

                    <div class="area-card">
                        <div class="area-icon">💰</div>
                        <h4>Crimes Econômicos e Contra o Sistema Financeiro</h4>
                        <p>Defesa em processos complexos como lavagem de dinheiro, evasão de divisas, sonegação fiscal e crimes contra a ordem econômica.</p>
                    </div>

                    <div class="area-card">
                        <div class="area-icon">👷</div>
                        <h4>Crimes nas Relações de Trabalho e Previdência Social</h4>
                        <p>Atuação em casos envolvendo fraudes trabalhistas, acidentes laborais com responsabilidade penal, crimes contra a seguridade social e benefícios indevidos.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        let currentSlideIndex = 0;
        const slides = document.querySelectorAll('.slide');
        const indicators = document.querySelectorAll('.indicator');
        let autoSlideInterval;

        function showSlide(index) {
            requestAnimationFrame(() => {
                slides.forEach((slide, i) => {
                    slide.classList.remove('active', 'prev');
                    if (i < index) {
                        slide.classList.add('prev');
                    }
                });
                indicators.forEach(indicator => indicator.classList.remove('active'));

                slides[index].classList.add('active');
                indicators[index].classList.add('active');
            });
        }

        function changeSlide(direction) {
            currentSlideIndex += direction;

            if (currentSlideIndex >= slides.length) {
                currentSlideIndex = 0;
            } else if (currentSlideIndex < 0) {
                currentSlideIndex = slides.length - 1;
            }

            showSlide(currentSlideIndex);
        }

        function currentSlide(index) {
            currentSlideIndex = index - 1;
            showSlide(currentSlideIndex);
        }


        function startAutoSlide() {
            autoSlideInterval = setInterval(() => changeSlide(1), 6000);
        }

        const sliderContainer = document.querySelector('.slider-container');

        sliderContainer.addEventListener('mouseenter', () => clearInterval(autoSlideInterval));
        sliderContainer.addEventListener('mouseleave', startAutoSlide);

        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') changeSlide(-1);
            if (e.key === 'ArrowRight') changeSlide(1);
        });



        let startX = 0;
        let startY = 0;
        let isDragging = false;

        sliderContainer.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            isDragging = false;
        }, { passive: true });

        sliderContainer.addEventListener('touchmove', (e) => {
            if (!isDragging) {
                const diffX = Math.abs(e.touches[0].clientX - startX);
                const diffY = Math.abs(e.touches[0].clientY - startY);

                if (diffX > diffY && diffX > 10) {
                    isDragging = true;
                    e.preventDefault();
                }
            } else {
                e.preventDefault();
            }
        });

        sliderContainer.addEventListener('touchend', (e) => {
            if (isDragging) {
                const diff = startX - e.changedTouches[0].clientX;
                if (Math.abs(diff) > 50) {
                    changeSlide(diff > 0 ? 1 : -1);
                }
                e.preventDefault();
            }
            isDragging = false;
        }, { passive: false });

    
        startAutoSlide();


        function toggleMenu() {
            const menu = document.getElementById('sideMenu');
            const isActive = menu.classList.contains('active');

            if (isActive) {
                menu.classList.remove('active');
            } else {
                menu.classList.add('active');
            }
        }

        function closeMenu() {
            const menu = document.getElementById('sideMenu');
            menu.classList.remove('active');
        }

        function toggleSearch() {
            const searchContainer = document.querySelector('.search-container');
            const searchInput = document.getElementById('searchInput');
            const isActive = searchContainer.classList.contains('active');

            if (isActive) {
                searchContainer.classList.remove('active');
            } else {
                searchContainer.classList.add('active');
                setTimeout(() => {
                    searchInput.focus();
                }, 400);
            }
        }

        document.addEventListener('click', function(event) {
            const searchContainer = document.querySelector('.search-container');
            const isClickInside = searchContainer.contains(event.target);

            if (!isClickInside && searchContainer.classList.contains('active')) {
                searchContainer.classList.remove('active');
            }
        });
    </script>
</body>
</html>