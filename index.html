<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>projeto lafa</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <div class="menu-icon" onclick="toggleMenu()">
            <span></span>
            <span></span>
            <span></span>
        </div>
        <div class="header-logo">
            <img src="img/Logo Principal s Fundo (1).png?v=1" alt="Logo">
        </div>
        <div class="search-container">
            <input type="text" class="search-input" placeholder="Pesquisar..." id="searchInput">
            <div class="search-icon" onclick="toggleSearch()">⌕</div>
        </div>
    </header>

    <div class="blur-bar"></div>

  
    <nav class="side-menu" id="sideMenu">
        <div class="menu-overlay" onclick="closeMenu()"></div>
        <div class="menu-content">
            <div class="menu-header">
                <h2>Menu</h2>
                <button class="close-btn" onclick="closeMenu()">×</button>
            </div>
            <ul class="menu-items">
                <li><a href="#home">Home</a></li>
                <li><a href="#quem-somos">Quem Somos</a></li>
                <li><a href="#areas-atuacao">Áreas de Atuação</a></li>
                <li><a href="#contatos">Contatos</a></li>
            </ul>
        </div>
    </nav>

   
    <section class="info-slider">
        <div class="slider-container">
            <div class="slider-wrapper">
                <div class="slide active">
                    <div class="slide-content">
                        <img src="img/patrick-fore-H5Lf0nGyetk-unsplash.jpg" alt="Direito Criminal">
                        <div class="slide-text">
                            <h2>Atendimento Jurídico na Área Penal</h2>
                        </div>
                    </div>
                </div>
                <div class="slide">
                    <div class="slide-content">
                        <img src="img/giammarco-boscaro-eWpBNXRHfTI-unsplash.jpg">
                    </div>
                </div>
                <div class="slide">
                    <div class="slide-content">
                        <img src="img/nasser-eledroos-20fmGtxKs_I-unsplash.jpg">
                    </div>
                </div>
                <div class="slide">
                    <div class="slide-content">
                        <img src="img/pexels-ekaterina-bolovtsova-6077181.jpg">
                    </div>
                </div>
            </div>

            
            <button class="slider-btn prev-btn" onclick="changeSlide(-1)">‹</button>
            <button class="slider-btn next-btn" onclick="changeSlide(1)">›</button>


            <div class="slider-indicators">
                <span class="indicator active" onclick="currentSlide(1)"></span>
                <span class="indicator" onclick="currentSlide(2)"></span>
                <span class="indicator" onclick="currentSlide(3)"></span>
                <span class="indicator" onclick="currentSlide(4)"></span>
            </div>
        </div>
    </section>
    

    <script>
        let currentSlideIndex = 0;
        const slides = document.querySelectorAll('.slide');
        const indicators = document.querySelectorAll('.indicator');
        let autoSlideInterval;

        function showSlide(index) {
            requestAnimationFrame(() => {
                slides.forEach((slide, i) => {
                    slide.classList.remove('active', 'prev');
                    if (i < index) {
                        slide.classList.add('prev');
                    }
                });
                indicators.forEach(indicator => indicator.classList.remove('active'));

                slides[index].classList.add('active');
                indicators[index].classList.add('active');
            });
        }

        function changeSlide(direction) {
            currentSlideIndex += direction;

            if (currentSlideIndex >= slides.length) {
                currentSlideIndex = 0;
            } else if (currentSlideIndex < 0) {
                currentSlideIndex = slides.length - 1;
            }

            showSlide(currentSlideIndex);
        }

        function currentSlide(index) {
            currentSlideIndex = index - 1;
            showSlide(currentSlideIndex);
        }


        function startAutoSlide() {
            autoSlideInterval = setInterval(() => changeSlide(1), 6000);
        }

        const sliderContainer = document.querySelector('.slider-container');

        sliderContainer.addEventListener('mouseenter', () => clearInterval(autoSlideInterval));
        sliderContainer.addEventListener('mouseleave', startAutoSlide);

        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') changeSlide(-1);
            if (e.key === 'ArrowRight') changeSlide(1);
        });



        let startX = 0;
        let startY = 0;
        let isDragging = false;

        sliderContainer.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            isDragging = false;
        }, { passive: true });

        sliderContainer.addEventListener('touchmove', (e) => {
            if (!isDragging) {
                const diffX = Math.abs(e.touches[0].clientX - startX);
                const diffY = Math.abs(e.touches[0].clientY - startY);

                if (diffX > diffY && diffX > 10) {
                    isDragging = true;
                    e.preventDefault();
                }
            } else {
                e.preventDefault();
            }
        });

        sliderContainer.addEventListener('touchend', (e) => {
            if (isDragging) {
                const diff = startX - e.changedTouches[0].clientX;
                if (Math.abs(diff) > 50) {
                    changeSlide(diff > 0 ? 1 : -1);
                }
                e.preventDefault();
            }
            isDragging = false;
        }, { passive: false });

    
        startAutoSlide();


        function toggleMenu() {
            const menu = document.getElementById('sideMenu');
            const isActive = menu.classList.contains('active');

            if (isActive) {
                menu.classList.remove('active');
            } else {
                menu.classList.add('active');
            }
        }

        function closeMenu() {
            const menu = document.getElementById('sideMenu');
            menu.classList.remove('active');
        }

        function toggleSearch() {
            const searchContainer = document.querySelector('.search-container');
            const searchInput = document.getElementById('searchInput');
            const isActive = searchContainer.classList.contains('active');

            if (isActive) {
                searchContainer.classList.remove('active');
            } else {
                searchContainer.classList.add('active');
                setTimeout(() => {
                    searchInput.focus();
                }, 400);
            }
        }

        document.addEventListener('click', function(event) {
            const searchContainer = document.querySelector('.search-container');
            const isClickInside = searchContainer.contains(event.target);

            if (!isClickInside && searchContainer.classList.contains('active')) {
                searchContainer.classList.remove('active');
            }
        });
    </script>
</body>
</html>