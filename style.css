@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}



body {
    font-family: 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    min-height: 100vh;
    overflow-x: hidden;
    background: #D7BDA6;
}

header {
    background: linear-gradient(135deg, #AB7743 0%, #6D3914 100%);
    height: 80px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    box-shadow: 0 4px 20px rgba(76, 43, 8, 0.4);
}

.blur-bar {
    position: fixed;
    top: 80px;
    left: 0;
    width: 100%;
    height: 8px;
    background: linear-gradient(135deg,
        rgba(171, 119, 67, 0.6) 0%,
        rgba(109, 57, 20, 0.7) 50%,
        rgba(171, 119, 67, 0.6) 100%);
    backdrop-filter: blur(8px);
    z-index: 999;
    box-shadow: 0 2px 10px rgba(76, 43, 8, 0.3);
}

.header-logo {
    position: absolute;
    left: 50%;
    top: 40%;
    transform: translate(-50%, -50%);
}

.header-logo img {
    height: 70px;
    width: auto;
    object-fit: contain;
    filter: drop-shadow(0 4px 8px rgba(76, 43, 8, 0.4));
}

.menu-icon {
    width: 30px;
    height: 30px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
}

.menu-icon span {
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #D7BDA6, #B7957F);
    border-radius: 2px;
    transition: 0.3s ease;
    box-shadow: 0 1px 3px rgba(76, 43, 8, 0.3);
}

.menu-icon:hover span {
    transform: scaleX(1.2);
    background: linear-gradient(90deg, #B7957F, #AB7743);
    box-shadow: 0 2px 6px rgba(76, 43, 8, 0.5);
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    width: 0;
    padding: 8px 12px;
    border: 2px solid transparent;
    border-radius: 25px;
    background: rgba(215, 189, 166, 0.1);
    color: #D7BDA6;
    font-size: 16px;
    outline: none;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    opacity: 0;
    transform: translateX(20px);
}

.search-input::placeholder {
    color: rgba(215, 189, 166, 0.6);
}

.search-input:focus {
    border-color: #B7957F;
    background: rgba(215, 189, 166, 0.15);
}

.search-container.active .search-input {
    width: 200px;
    opacity: 1;
    transform: translateX(0);
    margin-right: 10px;
}

.search-icon {
    color: #D7BDA6;
    font-size: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
    transform: rotate(5deg);
    text-shadow: 0 2px 4px rgba(76, 43, 8, 0.5);
    filter: drop-shadow(0 0 8px rgba(215, 189, 166, 0.3));
    z-index: 10;
}

.search-icon:hover {
    color: #B7957F;
    transform: rotate(5deg) scale(1.1);
    filter: drop-shadow(0 0 12px rgba(215, 189, 166, 0.6));
}

/* Menu Lateral */
.side-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease;
}

.side-menu.active {
    visibility: visible;
    opacity: 1;
}

.menu-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(76, 43, 8, 0.8);
    transition: background 0.3s ease;
}

.menu-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 300px;
    height: 100%;
    background: linear-gradient(180deg, #AB7743 0%, #6D3914 50%, #4C2B08 100%);
    box-shadow: 5px 0 20px rgba(76, 43, 8, 0.5);
    transform: translateX(-100%);
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.side-menu.active .menu-content {
    transform: translateX(0);
}

.menu-header {
    padding: 30px 25px 20px;
    border-bottom: 2px solid rgba(215, 189, 166, 0.3);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.menu-header h2 {
    color: #D7BDA6;
    font-size: 24px;
    font-weight: 600;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    color: #D7BDA6;
    font-size: 32px;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-btn:hover {
    color: #B7957F;
    transform: scale(1.1);
}

.menu-items {
    list-style: none;
    padding: 20px 0;
    margin: 0;
}

.menu-items li {
    margin: 0;
}

.menu-items a {
    display: block;
    padding: 18px 25px;
    color: #D7BDA6;
    text-decoration: none;
    font-size: 18px;
    font-weight: 500;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
    opacity: 0;
    transform: translateX(-30px);
}

.side-menu.active .menu-items a {
    opacity: 1;
    transform: translateX(0);
}

.side-menu.active .menu-items a:nth-child(1) { transition-delay: 0.1s; }
.side-menu.active .menu-items a:nth-child(2) { transition-delay: 0.2s; }
.side-menu.active .menu-items a:nth-child(3) { transition-delay: 0.3s; }
.side-menu.active .menu-items a:nth-child(4) { transition-delay: 0.4s; }

.menu-items a:hover {
    background: rgba(215, 189, 166, 0.1);
    color: #B7957F;
    border-left-color: #B7957F;
    padding-left: 35px;
    transform: translateX(10px);
}


.info-slider {
    margin-top: 88px;
    height: 500px;
    position: relative;
    overflow: hidden;
    background: linear-gradient(180deg, #AB7743 0%, #6D3914 50%, #4C2B08 100%);
    box-shadow: 0 15px 50px rgba(76, 43, 8, 0.4);
    touch-action: pan-y;
    min-height: 300px;
    max-height: 80vh;
}

.slider-container {
    position: relative;
    width: 100%;
    height: 100%;
    touch-action: pan-x;
    user-select: none;
}

.slider-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translateX(100%);
    transition: transform 0.5s ease-out, opacity 0.5s ease-out;
    display: flex;
    align-items: center;
    justify-content: center;
    will-change: transform, opacity;
}

.slide.active {
    opacity: 1;
    transform: translateX(0);
}

.slide.prev {
    transform: translateX(-100%);
}

.slide-content {
    max-width: 100%;
    width: 100%;
    padding: 20px 0;
    text-align: center;
    color: #D7BDA6;
    position: relative;
}

.slide-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    text-align: center;
}

.slide-text h2 {
    margin: 0;
    font-family: 'Poppins', sans-serif;
    font-size: 18px;
    font-weight: 600;
    color: #D7BDA6;
    text-shadow:
        0 4px 8px rgba(76, 43, 8, 0.9),
        0 2px 4px rgba(0, 0, 0, 0.8);
    letter-spacing: 3px;
    text-transform: uppercase;
}



.slide-content img {
    width: 100%;
    height: 70vh;
    object-fit: cover;
    object-position: center 60%;
    box-shadow: 0 10px 30px rgba(76, 43, 8, 0.5);
    transition: transform 0.3s ease;
    will-change: transform;
}

.slide-content img::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        45deg,
        rgba(171, 119, 67, 0.1) 0%,
        transparent 30%,
        transparent 70%,
        rgba(109, 57, 20, 0.1) 100%
    );

    pointer-events: none;
}

.slide-content img:hover {
    transform: scale(1.02);
}




img[src*="giammarco-boscaro"] {
    object-position: center 30% !important;
}


img[src*="pexels-ekaterina-bolovtsova"] {
    object-position: center 25% !important;
}


.quem-somos {
    padding: 80px 0;
    background: linear-gradient(135deg, #D7BDA6 0%, #B7957F 50%, #AB7743 100%);
    position: relative;
    border-top: 4px solid #AB7743;
}

.quem-somos::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #6D3914, transparent);
}



.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
}

.quem-somos-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 60px;
    align-items: center;
    position: relative;
    z-index: 1;
}

.text-content h2 {
    font-family: 'Poppins', sans-serif;
    font-size: 42px;
    font-weight: 700;
    color: #4C2B08;
    margin-bottom: 40px;
    text-shadow: 0 2px 4px rgba(76, 43, 8, 0.3);
    position: relative;
}

.text-content h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #6D3914, #AB7743);
    border-radius: 2px;
}

.text-blocks p {
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    line-height: 1.8;
    color: #4C2B08;
    margin-bottom: 25px;
    text-align: justify;
    opacity: 0.9;
}

.text-blocks p strong {
    color: #6D3914;
    font-weight: 600;
}

.image-content {
    position: relative;
}

.image-placeholder {
    width: 100%;
    height: 450px;
    background: linear-gradient(145deg, rgba(215, 189, 166, 0.15), rgba(171, 119, 67, 0.1));
    border: 2px solid rgba(171, 119, 67, 0.3);
    border-radius: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
    box-shadow: 0 8px 25px rgba(76, 43, 8, 0.15);
}

.image-placeholder img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center 30%;
    border-radius: 23px;
    transition: all 0.3s ease;
}

.image-placeholder:hover img {
    transform: scale(1.02);
    object-position: center 25%;
}



.image-placeholder span {
    font-family: 'Poppins', sans-serif;
    font-size: 20px;
    color: #AB7743;
    font-weight: 600;
    z-index: 1;
    position: relative;
    text-shadow: 0 2px 4px rgba(76, 43, 8, 0.3);
    letter-spacing: 1px;
    display: none;
}

.image-placeholder:hover {
    border-color: rgba(171, 119, 67, 0.5);
    background: linear-gradient(145deg, rgba(215, 189, 166, 0.2), rgba(171, 119, 67, 0.15));
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(76, 43, 8, 0.2);
}

.nossa-equipe {
    padding: 120px 0;
    background: linear-gradient(180deg, #B7957F 0%, #AB7743 50%, #6D3914 100%);
    position: relative;
    overflow: hidden;
    border-top: 4px solid #6D3914;
}

.nossa-equipe::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #4C2B08, transparent);
    z-index: 10;
}

.nossa-equipe::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 20%, rgba(171, 119, 67, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(215, 189, 166, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.section-header {
    text-align: center;
    margin-bottom: 80px;
    position: relative;
    z-index: 1;
}

.section-header h2 {
    font-family: 'Poppins', sans-serif;
    font-size: 48px;
    font-weight: 800;
    color: #D7BDA6;
    margin-bottom: 30px;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
    position: relative;
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 4px;
    background: linear-gradient(90deg, #D7BDA6, #B7957F, #D7BDA6);
    border-radius: 2px;
}

.section-intro {
    font-family: 'Poppins', sans-serif;
    font-size: 18px;
    line-height: 1.8;
    color: #D7BDA6;
    max-width: 800px;
    margin: 0 auto;
    opacity: 0.9;
    text-align: center;
}

.team-category {
    margin-bottom: 80px;
    position: relative;
    z-index: 1;
}

.category-title {
    font-family: 'Poppins', sans-serif;
    font-size: 32px;
    font-weight: 700;
    color: #D7BDA6;
    text-align: center;
    margin-bottom: 50px;
    position: relative;
}



.team-grid {
    display: grid;
    gap: 40px;
    position: relative;
}

.founders {
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
}

.associate {
    grid-template-columns: 1fr;
    max-width: 600px;
    margin: 0 auto;
}

.org-tree {
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.tree-level {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 60px;
    position: relative;
}

.level-1 {
    gap: 200px;
}

.level-2 {
    gap: 0;
    margin-bottom: 80px;
}

.level-3 {
    gap: 50px;
    flex-wrap: wrap;
}

.tree-node {
    background: linear-gradient(145deg, rgba(215, 189, 166, 0.12), rgba(171, 119, 67, 0.08));
    border: 3px solid rgba(215, 189, 166, 0.2);
    border-radius: 25px;
    padding: 25px 20px;
    text-align: center;
    position: relative;
    transition: all 0.4s ease;
    width: 250px;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
}

.tree-node.founder {
    border-color: rgba(171, 119, 67, 0.4);
    background: linear-gradient(145deg, rgba(171, 119, 67, 0.15), rgba(109, 57, 20, 0.1));
    box-shadow: 0 8px 25px rgba(171, 119, 67, 0.2);
}

.tree-node.associate {
    border-color: rgba(183, 149, 127, 0.4);
    background: linear-gradient(145deg, rgba(183, 149, 127, 0.12), rgba(171, 119, 67, 0.08));
    box-shadow: 0 8px 25px rgba(183, 149, 127, 0.2);
}

.tree-node.admin {
    border-color: rgba(215, 189, 166, 0.3);
    background: linear-gradient(145deg, rgba(215, 189, 166, 0.1), rgba(183, 149, 127, 0.06));
    box-shadow: 0 6px 20px rgba(215, 189, 166, 0.15);
}

.tree-node:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.node-photo {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(215, 189, 166, 0.3), rgba(171, 119, 67, 0.2));
    border: 3px solid rgba(215, 189, 166, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px auto;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.node-photo span {
    font-family: 'Poppins', sans-serif;
    font-size: 12px;
    color: #D7BDA6;
    font-weight: 500;
}

.node-photo img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    object-position: center;
    transition: all 0.3s ease;
}

.node-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    width: 100%;
}

.node-info h4 {
    font-family: 'Poppins', sans-serif;
    font-size: 18px;
    font-weight: 700;
    color: #D7BDA6;
    margin-bottom: 10px;
    line-height: 1.3;
    word-wrap: break-word;
    hyphens: auto;
}

.node-position {
    font-family: 'Poppins', sans-serif;
    font-size: 12px;
    font-weight: 500;
    color: #B7957F;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.9;
    line-height: 1.4;
    word-wrap: break-word;
}

.slider-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: rgba(215, 189, 166, 0.8);
    font-size: 24px;
    width: 60px;
    height: 60px;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    text-shadow: 0 2px 8px rgba(76, 43, 8, 0.6);
}

.slider-btn:hover {
    color: #B7957F;
    transform: translateY(-50%) scale(1.2);
    text-shadow: 0 4px 12px rgba(76, 43, 8, 0.8);
}

.slider-btn:active {
    transform: translateY(-50%) scale(1.1);
    color: #AB7743;
}

.prev-btn {
    left: 20px;
}

.next-btn {
    right: 20px;
}


.slider-indicators {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 20px;
    z-index: 100;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(215, 189, 166, 0.4);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background: linear-gradient(135deg, #AB7743, #B7957F);
    transform: scale(1.2);
}

.indicator:hover {
    background: rgba(215, 189, 166, 0.7);
}


@media (max-width: 768px) {
    .info-slider {
        height: 350px;
        touch-action: pan-y;
    }

    .slider-container {
        touch-action: pan-x;
    }

    .slide-content {
        padding: 10px 0;
        margin: 0;
    }

    .slide-content img {
        height: 50vh;
    }

    .slide-text h2 {
        font-size: 18px;
        letter-spacing: 1px;
    }

    .slider-btn {
        width: 50px;
        height: 50px;
        font-size: 40px;
        touch-action: manipulation;
    }

    .prev-btn {
        left: 15px;
    }

    .next-btn {
        right: 15px;
    }

    .slider-indicators {
        gap: 12px;
    }

    .indicator {
        width: 10px;
        height: 10px;
    }

    .menu-content {
        width: 280px;
    }

    .menu-header {
        padding: 25px 20px 15px;
    }

    .menu-header h2 {
        font-size: 20px;
    }

    .menu-items a {
        padding: 15px 20px;
        font-size: 16px;
    }

    .menu-items a:hover {
        padding-left: 30px;
    }

    .search-container.active .search-input {
        width: 150px;
        font-size: 14px;
    }

    .search-icon {
        font-size: 24px;
    }

    .quem-somos {
        padding: 60px 0;
    }

    .container {
        padding: 0 20px;
    }

    .quem-somos-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .text-content h2 {
        font-size: 32px;
        margin-bottom: 30px;
    }

    .text-blocks p {
        font-size: 15px;
        line-height: 1.7;
        margin-bottom: 20px;
    }

    .image-placeholder {
        height: 280px;
        border-radius: 20px;
        box-shadow: 0 6px 20px rgba(76, 43, 8, 0.12);
    }

    .image-placeholder span {
        font-size: 18px;
        letter-spacing: 0.5px;
    }
}

.atuacao-section {
    padding: 120px 0;
    background: linear-gradient(135deg, #6D3914 0%, #4C2B08 50%, #2A1804 100%);
    position: relative;
    overflow: hidden;
    border-top: 4px solid #4C2B08;
}

.atuacao-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 25% 25%, rgba(171, 119, 67, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(183, 149, 127, 0.08) 0%, transparent 50%);
    pointer-events: none;
}

.atuacao-section h2 {
    font-family: 'Poppins', sans-serif;
    font-size: 56px;
    font-weight: 800;
    text-align: center;
    margin-bottom: 30px;
    color: #D7BDA6;
    text-shadow: 0 4px 20px rgba(215, 189, 166, 0.3);
    position: relative;
    z-index: 2;
}

.atuacao-section h2::after {
    content: '';
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 150px;
    height: 6px;
    background: linear-gradient(90deg, transparent, #AB7743, #D7BDA6, #AB7743, transparent);
    border-radius: 3px;
}

.atuacao-section .intro {
    font-family: 'Poppins', sans-serif;
    font-size: 20px;
    line-height: 1.8;
    text-align: center;
    margin-bottom: 30px;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
    color: #D7BDA6;
    opacity: 0.95;
    position: relative;
    z-index: 2;
}

.atuacao-section h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 36px;
    font-weight: 700;
    text-align: center;
    margin: 80px 0 60px 0;
    color: #D7BDA6;
    position: relative;
    z-index: 2;
}

.atuacao-grid {
    display: flex;
    flex-direction: column;
    gap: 0;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.atuacao-item {
    display: flex;
    align-items: center;
    padding: 40px 0;
    border-bottom: 1px solid rgba(215, 189, 166, 0.1);
    position: relative;
    transition: all 0.4s ease;
    cursor: pointer;
}

.atuacao-item:last-child {
    border-bottom: none;
}

.atuacao-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, rgba(215, 189, 166, 0.1), rgba(171, 119, 67, 0.05));
    transition: width 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: -1;
}

.atuacao-item:hover::before {
    width: 100%;
}

.atuacao-item:hover {
    padding-left: 30px;
    border-left: 4px solid #AB7743;
}

.area-number {
    display: none;
}

.area-content {
    flex: 1;
}

.atuacao-item h4 {
    font-family: 'Poppins', sans-serif;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 15px;
    color: #D7BDA6;
    position: relative;
    transition: all 0.3s ease;
}

.atuacao-item:hover h4 {
    color: #AB7743;
    transform: translateX(10px);
}

.atuacao-item h4::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, #AB7743, #D7BDA6);
    transition: width 0.4s ease;
}

.atuacao-item:hover h4::after {
    width: 100px;
}

.atuacao-item p {
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    line-height: 1.7;
    color: #D7BDA6;
    opacity: 0.85;
    transition: all 0.3s ease;
    max-width: 800px;
}

.atuacao-item:hover p {
    opacity: 1;
    transform: translateX(10px);
}

.area-icon {
    font-size: 48px;
    margin-left: auto;
    margin-right: 0;
    opacity: 0.7;
    transition: all 0.4s ease;
    filter: grayscale(100%);
    min-width: 80px;
    text-align: center;
}

.atuacao-item:hover .area-icon {
    opacity: 1;
    transform: scale(1.1);
    filter: grayscale(0%);
}

    .nossa-equipe {
        padding: 80px 0;
    }

    .section-header {
        margin-bottom: 60px;
    }

    .section-header h2 {
        font-size: 36px;
        margin-bottom: 25px;
    }

    .section-intro {
        font-size: 16px;
        line-height: 1.6;
        padding: 0 10px;
    }

    .team-category {
        margin-bottom: 60px;
    }

    .category-title {
        font-size: 24px;
        margin-bottom: 40px;
    }

    .tree-node {
        width: 300px;
        min-height: 180px;
        padding: 20px 15px;
        border-radius: 20px;
    }

    .node-photo {
        width: 80px;
        height: 80px;
        margin-bottom: 15px;
    }

    .node-info h4 {
        font-size: 16px;
        margin-bottom: 8px;
    }

    .node-position {
        font-size: 11px;
        letter-spacing: 0.3px;
    }

    .photo-placeholder {
        width: 100px;
        height: 100px;
    }

    .member-info h4 {
        font-size: 20px;
    }

    .position {
        font-size: 13px;
        margin-bottom: 12px;
    }

    .member-info p {
        font-size: 14px;
        line-height: 1.6;
    }

    .atuacao-section {
        padding: 60px 0;
    }

    .atuacao-section h2 {
        font-size: 32px;
        margin-bottom: 30px;
    }

    .atuacao-section .intro {
        font-size: 15px;
        margin-bottom: 20px;
        padding: 0 10px;
    }

    .atuacao-section h3 {
        font-size: 24px;
        margin: 40px 0 30px 0;
    }

    .atuacao-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 0 10px;
    }

    .atuacao-item {
        padding: 25px 20px;
    }

    .atuacao-item h4 {
        font-size: 16px;
        margin-bottom: 12px;
    }

    .atuacao-section {
        padding: 60px 0;
    }

    .atuacao-section h2 {
        font-size: 32px;
    }

    .atuacao-section .intro {
        font-size: 15px;
        padding: 0 20px;
    }

    .atuacao-section h3 {
        font-size: 24px;
    }

    .atuacao-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 0 20px;
    }

    .atuacao-item {
        flex-direction: column;
        text-align: center;
        padding: 30px 20px;
    }



    .atuacao-item h4 {
        font-size: 22px;
        margin-bottom: 15px;
    }

    .atuacao-item p {
        font-size: 14px;
        line-height: 1.6;
    }

    .area-icon {
        margin-left: 0;
        margin-top: 20px;
        font-size: 36px;
    }

    .atuacao-item:hover {
        padding-left: 20px;
        border-left: none;
        border-top: 4px solid #AB7743;
    }

    .atuacao-item:hover h4,
    .atuacao-item:hover p {
        transform: none;
    }
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #2A1804 0%, #1A0F08 100%);
    padding: 60px 0 30px 0;
    border-top: 4px solid #AB7743;
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #AB7743, transparent);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h4 {
    font-family: 'Poppins', sans-serif;
    font-size: 20px;
    font-weight: 700;
    color: #D7BDA6;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.footer-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.footer-item:hover {
    transform: translateX(5px);
}

.footer-icon {
    font-size: 18px;
    margin-right: 12px;
    margin-top: 2px;
    flex-shrink: 0;
}

.footer-item span:last-child {
    font-family: 'Poppins', sans-serif;
    font-size: 15px;
    color: #D7BDA6;
    line-height: 1.6;
    opacity: 0.9;
}

.footer-bottom {
    border-top: 1px solid rgba(215, 189, 166, 0.2);
    padding-top: 30px;
    text-align: center;
}

.footer-bottom p {
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    color: #B7957F;
    margin: 0;
    opacity: 0.8;
}

/* Footer Mobile */
@media (max-width: 768px) {
    .footer {
        padding: 50px 0 25px 0;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
        padding: 0 20px;
        text-align: center;
    }

    .footer-section h4 {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .footer-item {
        justify-content: center;
        margin-bottom: 12px;
    }

    .footer-item span:last-child {
        font-size: 14px;
    }

    .footer-bottom {
        padding-top: 25px;
        margin: 0 20px;
    }

    .footer-bottom p {
        font-size: 13px;
    }
}
